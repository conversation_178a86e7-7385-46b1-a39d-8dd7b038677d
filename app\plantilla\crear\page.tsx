"use client";

import React, { Suspense, useState, useEffect } from "react";
import { But<PERSON> } from "@heroui/react";
import { useRouter } from "next/navigation";

import { title } from "@/components/primitives";
import TemplateStepper from "@/components/template/create/template-stepper";
import SelectableFieldsTable from "@/components/template/create/selectable-fields-table";
import SelectableRulesTable from "@/components/template/create/selectable-rules-table";
import SelectableNotificationsTable from "@/components/template/create/selectable-notifications-table";
import TemplateSummary from "@/components/template/create/template-summary";
import { useTemplateDetail } from "@/hooks/templates/useTemplateDetail";

// Create a separate client component that uses searchParams
function TemplateContent() {
  // useSearchParams is used inside this component
  const { useSearchParams } = require("next/navigation");
  const searchParams = useSearchParams();
  const router = useRouter();
  const {
    templateDetail,
    loading: templateLoading,
    fetchTemplateDetail,
  } = useTemplateDetail();

  const templateData = {
    id: searchParams.get("id") || "",
    name: searchParams.get("title") || templateDetail?.name || "",
    description:
      searchParams.get("description") || templateDetail?.description || "",
    type: searchParams.get("type") || "",
  };

  const [currentStep, setCurrentStep] = useState(1);
  const [selectedFields, setSelectedFields] = useState<string[]>([]);
  const [selectedRules, setSelectedRules] = useState<string[]>([]);
  const [selectedNotifications, setSelectedNotifications] = useState<string[]>(
    [],
  );
  const [isTemplateLoaded, setIsTemplateLoaded] = useState(false);

  // Effect to fetch template details if ID is present in searchParams
  useEffect(() => {
    const templateId = searchParams.get("id");

    if (templateId && !isTemplateLoaded) {
      fetchTemplateDetail(templateId).then((data) => {
        if (data && data.full_definition) {
          // Auto-populate selected fields
          const fieldIds = data.full_definition.fields.map(
            (field: { id: { toString: () => any } }) => field.id.toString(),
          );

          setSelectedFields(fieldIds);

          // Auto-populate selected rules
          const ruleIds = data.full_definition.rules.map(
            (rule: { id: { toString: () => any } }) => rule.id.toString(),
          );

          setSelectedRules(ruleIds);

          // Auto-populate selected notifications
          const notificationIds = data.full_definition.notifications.map(
            (notification: { id: { toString: () => any } }) =>
              notification.id.toString(),
          );

          setSelectedNotifications(notificationIds);

          setIsTemplateLoaded(true);
        }
      });
    }
  }, [searchParams, fetchTemplateDetail, isTemplateLoaded]);

  // Show loading while fetching template details
  if (templateLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <p className="text-default-500">Cargando plantilla...</p>
      </div>
    );
  }

  if (
    !templateData.name &&
    !templateData.description &&
    !templateData.type &&
    !templateData.id
  ) {
    return (
      <div className="flex items-center justify-center h-screen">
        <p className="text-default-500">No template data available</p>
      </div>
    );
  }

  const steps = [
    {
      key: 1,
      title: "Seleccionar campos",
      description: "Elige los campos para la plantilla",
    },
    {
      key: 2,
      title: "Seleccionar reglas",
      description: "Elige las reglas para la plantilla",
    },
    {
      key: 3,
      title: "Seleccionar notificaciones",
      description: "Elige las notificaciones para la plantilla",
    },
    {
      key: 4,
      title: "Confirmar creación",
      description: "Revisa y confirma la plantilla",
    },
  ];

  const handleNext = () => {
    if (currentStep < 4) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleStepClick = (step: number) => {
    setCurrentStep(step);
  };

  const handleCreateTemplate = () => {
    // TODO: Implement template creation logic
    console.log(
      JSON.stringify(
        {
          templateData,
          selectedFields,
          selectedRules,
          selectedNotifications,
        },
        null,
        2,
      ),
    );
    // Redirect to templates page or show success message
    router.push("/configuracion");
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <SelectableFieldsTable
            selectedFields={selectedFields}
            onSelectionChange={setSelectedFields}
          />
        );
      case 2:
        return (
          <SelectableRulesTable
            selectedRules={selectedRules}
            selectedFields={selectedFields}
            onSelectionChange={setSelectedRules}
          />
        );
      case 3:
        return (
          <SelectableNotificationsTable
            selectedNotifications={selectedNotifications}
            onSelectionChange={setSelectedNotifications}
          />
        );
      case 4:
        return (
          <TemplateSummary
            selectedFields={selectedFields}
            selectedNotifications={selectedNotifications}
            selectedRules={selectedRules}
            templateData={templateData}
            onCreateTemplate={handleCreateTemplate}
          />
        );
      default:
        return null;
    }
  };

  const currentStepData = steps.find((step) => step.key === currentStep);
  const isLastStep = currentStep === 4;
  const isFirstStep = currentStep === 1;

  return (
    <>
      <div className="flex justify-between items-center w-full pb-4">
        <div>
          <h2 className={title({ size: "sm" })}>
            Paso {currentStep}: {currentStepData?.title}
          </h2>
          <p className="text-default-500 mt-1">
            {currentStepData?.description}
          </p>
        </div>
        <div className="flex gap-2">
          {!isFirstStep && (
            <Button variant="bordered" onPress={handlePrevious}>
              Anterior
            </Button>
          )}
          {!isLastStep && (
            <Button color="primary" onPress={handleNext}>
              Siguiente
            </Button>
          )}
        </div>
      </div>

      <TemplateStepper
        currentStep={currentStep}
        steps={steps}
        templateData={templateData}
        onStepClick={handleStepClick}
      />

      {renderStepContent()}
    </>
  );
}

// Main page component
export default function CrearPlantillaPage() {
  return (
    <Suspense fallback={<p>Cargando...</p>}>
      <TemplateContent />
    </Suspense>
  );
}

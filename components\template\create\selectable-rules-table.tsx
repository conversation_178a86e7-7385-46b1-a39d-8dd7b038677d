import React, { useMemo, useState } from "react";
import {
  Input,
  Table,
  TableBody,
  TableCell,
  TableColumn,
  TableHeader,
  TableRow,
  Pagination,
  Chip,
  Spinner,
  Card,
  CardBody,
} from "@heroui/react";
import { Icon } from "@iconify/react";
import { useQuery } from "@apollo/client";

import { FilterDropdown } from "@/components/projects/projects-table/filter-dropdown";
import { AllRulesDocument, RuleType } from "@/graphql/schemas/generated";

interface SelectableRulesTableProps {
  selectedRules: string[];
  selectedFields: string[];
  onSelectionChange: (selectedRules: string[]) => void;
}

export default function SelectableRulesTable({
  selectedRules,
  selectedFields,
  onSelectionChange,
}: SelectableRulesTableProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [page, setPage] = useState(1);
  const [activeFilters, setActiveFilters] = useState<Record<string, string[]>>(
    {},
  );
  const [sortConfig, setSortConfig] = useState<{
    column: string;
    direction: "asc" | "desc";
  } | null>(null);

  const rowsPerPage = 10;

  const { data, loading, error } = useQuery(AllRulesDocument);

  const rules = data?.allRules || [];

  const filteredAndSortedRules = useMemo(() => {
    let filtered = rules.filter((rule: RuleType) => {
      if (!rule) return false;

      // Search filter
      const searchMatch =
        rule.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        rule.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        rule.action?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        rule.condition?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        rule.status?.toLowerCase().includes(searchTerm.toLowerCase());

      if (!searchMatch) return false;

      // Column filters
      for (const [column, values] of Object.entries(activeFilters)) {
        if (values.length === 0) continue;

        let fieldValue = "";

        switch (column) {
          case "action":
            fieldValue = rule.action || "";
            break;
          case "condition":
            fieldValue = rule.condition || "";
            break;
          case "status":
            fieldValue = rule.status || "";
            break;
          default:
            fieldValue = "";
        }

        if (!values.includes(fieldValue)) return false;
      }

      return true;
    });

    // Sort
    if (sortConfig) {
      filtered.sort((a: RuleType, b: RuleType) => {
        let aValue = "";
        let bValue = "";

        switch (sortConfig.column) {
          case "name":
            aValue = a.name || "";
            bValue = b.name || "";
            break;
          case "action":
            aValue = a.action || "";
            bValue = b.action || "";
            break;
          case "condition":
            aValue = a.condition || "";
            bValue = b.condition || "";
            break;
          case "status":
            aValue = a.status || "";
            bValue = b.status || "";
            break;
          default:
            return 0;
        }

        const comparison = aValue.localeCompare(bValue);

        return sortConfig.direction === "asc" ? comparison : -comparison;
      });
    }

    return filtered;
  }, [rules, searchTerm, activeFilters, sortConfig]);

  const pages = Math.ceil(filteredAndSortedRules.length / rowsPerPage);
  const items = useMemo(() => {
    const start = (page - 1) * rowsPerPage;
    const end = start + rowsPerPage;

    return filteredAndSortedRules.slice(start, end);
  }, [page, filteredAndSortedRules]);

  const handleSelectionChange = (keys: any) => {
    if (keys === "all") {
      const allKeys = filteredAndSortedRules.map((rule: RuleType) => rule.id);

      onSelectionChange(allKeys);
    } else {
      const newSelection = Array.from(keys).map((key) => String(key));

      onSelectionChange(newSelection);
    }
  };

  const handleFilterChange = (column: string, values: string[]) => {
    setActiveFilters((prev) => ({ ...prev, [column]: values }));
    setPage(1);
  };

  const handleSort = (column: string, direction: "asc" | "desc") => {
    setSortConfig({ column, direction });
  };

  const getUniqueValues = (column: string): string[] => {
    return Array.from(
      new Set(
        rules
          .map((rule: RuleType) => {
            switch (column) {
              case "action":
                return rule?.action || "";
              case "condition":
                return rule?.condition || "";
              case "status":
                return rule?.status || "";
              default:
                return "";
            }
          })
          .filter(Boolean) as string[],
      ),
    );
  };

  const getStatusColor = (status: string) => {
    switch (status?.toLowerCase()) {
      case "active":
      case "activo":
        return "success";
      case "inactive":
      case "inactivo":
        return "danger";
      case "draft":
      case "borrador":
        return "warning";
      default:
        return "default";
    }
  };

  if (loading) {
    return (
      <Card>
        <CardBody className="flex items-center justify-center py-10">
          <Spinner label="Cargando reglas..." />
        </CardBody>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardBody className="flex items-center justify-center py-10">
          <p className="text-danger">
            Error al cargar las reglas: {error.message}
          </p>
        </CardBody>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {/* Search */}
      <Input
        isClearable
        placeholder="Buscar reglas..."
        startContent={<Icon icon="heroicons:magnifying-glass" />}
        value={searchTerm}
        onClear={() => setSearchTerm("")}
        onValueChange={setSearchTerm}
      />

      {/* Selected count */}
      {selectedRules.length > 0 && (
        <Card className="bg-primary-50 dark:bg-primary-900">
          <CardBody className="py-2">
            <p className="text-primary-600 dark:text-primary-300 text-sm">
              {selectedRules.length} regla(s) seleccionada(s)
            </p>
          </CardBody>
        </Card>
      )}

      {/* Table */}
      <Table
        removeWrapper
        aria-label="Tabla de reglas seleccionables"
        bottomContent={
          pages > 1 ? (
            <div className="flex w-full justify-center">
              <Pagination
                isCompact
                showControls
                showShadow
                color="primary"
                page={page}
                total={pages}
                onChange={setPage}
              />
            </div>
          ) : null
        }
        selectedKeys={new Set(selectedRules)}
        selectionMode="multiple"
        onSelectionChange={handleSelectionChange}
      >
        <TableHeader>
          <TableColumn>
            <FilterDropdown
              activeFilters={activeFilters}
              column="name"
              items={getUniqueValues("name")}
              sortConfig={sortConfig}
              title="Nombre"
              onFilter={handleFilterChange}
              onSort={handleSort}
            />
          </TableColumn>
          <TableColumn>Descripción</TableColumn>
          <TableColumn>Campo origen</TableColumn>
          <TableColumn>
            <FilterDropdown
              activeFilters={activeFilters}
              column="condition"
              displayText={{
                CONTENT_EQUAL_TO: "Contenido igual a",
                CONTENT_UNEQUAL_TO: "Contenido no igual a",
                HAS_CONTENT: "Tiene contenido",
                IS_COMPLETED: "Estado completado",
                IS_NOT_COMPLETED: "Estado no completado",
                IN_PROGRESS: "Estado en progreso",
              }}
              items={getUniqueValues("condition")}
              sortConfig={sortConfig}
              title="Condición"
              onFilter={handleFilterChange}
              onSort={handleSort}
            />
          </TableColumn>
          <TableColumn>Campo destino</TableColumn>
          <TableColumn>
            <FilterDropdown
              activeFilters={activeFilters}
              column="action"
              items={getUniqueValues("action")}
              sortConfig={sortConfig}
              title="Acción"
              onFilter={handleFilterChange}
              onSort={handleSort}
            />
          </TableColumn>
          <TableColumn>
            <FilterDropdown
              activeFilters={activeFilters}
              column="status"
              items={getUniqueValues("status")}
              sortConfig={sortConfig}
              title="Estado"
              onFilter={handleFilterChange}
              onSort={handleSort}
            />
          </TableColumn>
        </TableHeader>
        <TableBody emptyContent="No hay reglas disponibles" items={items}>
          {(item: RuleType) => (
            <TableRow key={item.id}>
              <TableCell>{item.name}</TableCell>
              <TableCell>{item.description || "Sin descripción"}</TableCell>
              <TableCell>
                <span className="text-sm">
                  {item.originField?.name || "N/A"}
                </span>
              </TableCell>

              <TableCell>
                <Chip size="sm" variant="flat">
                  {item.condition === "CONTENT_EQUAL_TO"
                    ? "Contenido igual a"
                    : item.condition === "CONTENT_UNEQUAL_TO"
                      ? "Contenido no igual a"
                      : item.condition === "HAS_CONTENT"
                        ? "Tiene contenido"
                        : item.condition === "IS_COMPLETED"
                          ? "Estado completado"
                          : item.condition === "IS_NOT_COMPLETED"
                            ? "Estado no completado"
                            : item.condition === "IN_PROGRESS"
                              ? "Estado en progreso"
                              : item.condition}
                </Chip>
              </TableCell>
              <TableCell>
                <span className="text-sm">
                  {item.targetField?.name || "N/A"}
                </span>
              </TableCell>

              <TableCell>
                <Chip size="sm" variant="flat">
                  {item.action}
                </Chip>
              </TableCell>
              <TableCell>
                <Chip
                  color={getStatusColor(item.status)}
                  size="sm"
                  variant="flat"
                >
                  {item.status}
                </Chip>
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
    </div>
  );
}
